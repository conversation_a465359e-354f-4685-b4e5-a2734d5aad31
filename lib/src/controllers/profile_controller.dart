import 'dart:io';
import 'dart:developer' as developer;
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart' as dio;
import 'package:url_launcher/url_launcher.dart';
import 'package:image_picker/image_picker.dart';
import '../data/providers/api/api_provider.dart';
import '../data/models/profile/achievement.dart';
import '../data/models/profile/cv.dart';
import '../data/models/profile/experience.dart';
import '../data/models/profile/skill.dart';
import '../data/services/auth_service.dart';
import '../core/services/image_cropping_service.dart';
import '../core/services/debug_service.dart';

class ProfileController extends GetxController {
  final ApiProvider _apiProvider;
  final _dio = dio.Dio();

  ProfileController(this._apiProvider);

  ApiProvider get authenticatedApiProvider =>
      AppConfig.authenticatedApiProvider;

  ApiProvider get apiProvider => AppConfig.authenticatedApiProvider;

  // Observable variables
  final skills = <Skill>[].obs;
  final experiences = <Experience>[].obs;
  final achievements = <Achievement>[].obs;
  final cvs = <CV>[].obs;
  final Map<String, List<Skill>> _userSkillsMap = <String, List<Skill>>{}.obs;
  final Map<String, List<Experience>> _userExperiencesMap =
      <String, List<Experience>>{}.obs;
  final Map<String, List<Achievement>> _userAchievementsMap =
      <String, List<Achievement>>{}.obs;
  final Map<String, List<CV>> _userCVsMap = <String, List<CV>>{}.obs;
  final isLoading = false.obs;
  final currentUserId = RxString('');

  // Cache management
  final Duration _cacheTimeout = const Duration(minutes: 5);
  final Map<String, DateTime> _lastSkillsUpdateMap = <String, DateTime>{};
  final Map<String, DateTime> _lastExperiencesUpdateMap = <String, DateTime>{};
  final Map<String, DateTime> _lastAchievementsUpdateMap = <String, DateTime>{};
  final Map<String, DateTime> _lastCVsUpdateMap = <String, DateTime>{};

  @override
  void onInit() {
    super.onInit();

    // 🧪 DEBUG CONSOLE TEST - Multiple debug output methods
    print('🔍 ProfileController: onInit() called - Basic print statement');
    debugPrint('🐛 ProfileController: onInit() called - debugPrint statement');
    developer.log(
        '📝 ProfileController: onInit() called - developer.log statement',
        name: 'ProfileController');
    DebugService.info('ProfileController initialized',
        category: DebugService.categoryProfile);

    if (kDebugMode) {
      print(
          '🔧 ProfileController: Running in debug mode - kDebugMode conditional');
      DebugService.debug('ProfileController running in debug mode',
          category: DebugService.categoryProfile);
    }

    // Initialize controller similar to NewsController
    _loadInitialData();
  }

  // Initialize all profile data
  Future<void> _loadInitialData() async {
    if (!AuthService.instance.isAuthenticated()) {
      return;
    }
    await Future.wait([
      loadSkills(),
      loadExperiences(),
      loadAchievements(),
      loadCVs(),
    ]);
  }

  // Skills methods
  Future<void> loadSkills({String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastSkillsUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastSkillsUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userSkillsMap.containsKey(targetUserId)) {
        skills.value = _userSkillsMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await authenticatedApiProvider.getSkills(null)
          : await authenticatedApiProvider.getSkills(int.parse(targetUserId));

      _userSkillsMap[targetUserId] = response;
      skills.value = response;
      _lastSkillsUpdateMap[targetUserId] = DateTime.now();
    } catch (e) {
      CommonFunctions.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addSkill(String title, String description, int level) async {
    try {
      await authenticatedApiProvider.addSkill({
        'title': title,
        'description': description,
        'level': level,
      });
      await loadSkills(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> updateSkill(
      String skillId, String title, String description, int level) async {
    try {
      await authenticatedApiProvider.updateSkill(skillId, {
        'title': title,
        'description': description,
        'level': level,
      });
      await loadSkills(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> deleteSkill(String skillId) async {
    try {
      await authenticatedApiProvider.deleteSkill(skillId);
      await loadSkills(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // Experiences methods
  Future<void> loadExperiences(
      {String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastExperiencesUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastExperiencesUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userExperiencesMap.containsKey(targetUserId)) {
        experiences.value = _userExperiencesMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await apiProvider.getExperiences(null)
          : await apiProvider.getExperiences(int.parse(targetUserId));
      _userExperiencesMap[targetUserId] = response;
      experiences.value = response;
      _lastExperiencesUpdateMap[targetUserId] = DateTime.now();
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addExperience({
    required String title,
    required String organization,
    required DateTime startDate,
    DateTime? endDate,
    required String description,
    required String type,
    String? location,
    required bool isCurrentPosition,
  }) async {
    try {
      final data = {
        'job_title': title,
        'company_name': organization,
        'start_date': startDate.toIso8601String(),
        'end_date': endDate?.toIso8601String(),
        'description': description,
        'type': type,
        'location': location,
        'is_current_position': isCurrentPosition,
      };

      final response = await apiProvider.addExperience(data);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> updateExperience({
    required int id,
    String? title,
    String? organization,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
    String? type,
    String? location,
    bool? isCurrentPosition,
  }) async {
    try {
      final data = {
        if (title != null) 'job_title': title,
        if (organization != null) 'company_name': organization,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
        if (description != null) 'description': description,
        if (type != null) 'type': type,
        if (location != null) 'location': location,
        if (isCurrentPosition != null) 'is_current_position': isCurrentPosition,
      };

      final response = await apiProvider.updateExperience(id, data);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> deleteExperience(int id) async {
    try {
      final response = await apiProvider.deleteExperience(id);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // Achievements methods
  Future<void> loadAchievements(
      {String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastAchievementsUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastAchievementsUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userAchievementsMap.containsKey(targetUserId)) {
        achievements.value = _userAchievementsMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await apiProvider.getUserAchievements()
          : await apiProvider.getUserAchievementsById(targetUserId);

      if (response.success) {
        _userAchievementsMap[targetUserId] = response.achievements;
        achievements.value = response.achievements;
        _lastAchievementsUpdateMap[targetUserId] = DateTime.now();
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<Achievement> addAchievement(
    String title,
    String description, {
    required String type,
    required DateTime date,
    File? certificateFile,
    File? imageFile,
  }) async {
    try {
      final formData = dio.FormData.fromMap({
        'title': title,
        'description': description,
        'type': type,
        'date': date.toIso8601String(),
        if (certificateFile != null)
          'certificate': await dio.MultipartFile.fromFile(certificateFile.path),
        if (imageFile != null)
          'image': await dio.MultipartFile.fromFile(imageFile.path),
      });

      final response = await apiProvider.addAchievement(formData);
      if (!response.success) {
        throw Exception(response.message);
      }
      final achievement =
          Achievement.fromJson(response.data as Map<String, dynamic>);
      achievements.add(achievement);
      return achievement;
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> updateAchievement(
    String id, {
    String? title,
    String? description,
    String? type,
    DateTime? date,
    File? certificateFile,
    File? imageFile,
  }) async {
    try {
      final formData = dio.FormData.fromMap({
        if (title != null) 'title': title,
        if (description != null) 'description': description,
        if (type != null) 'type': type,
        if (date != null) 'date': date.toIso8601String(),
        if (certificateFile != null)
          'certificate': await dio.MultipartFile.fromFile(certificateFile.path),
        if (imageFile != null)
          'image': await dio.MultipartFile.fromFile(imageFile.path),
      });

      final response = await apiProvider.updateAchievement(id, formData);
      if (!response.success) {
        throw Exception(response.message);
      }
      final index = achievements.indexWhere((a) => a.id == id);
      if (index != -1) {
        achievements[index] =
            Achievement.fromJson(response.data as Map<String, dynamic>);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> deleteAchievement(String id) async {
    try {
      final response = await apiProvider.deleteAchievement(id);
      if (!response.success) {
        throw Exception(response.message);
      }
      achievements.removeWhere((a) => a.id == id);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // CVs methods
  Future<void> loadCVs({String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastCVsUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastCVsUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userCVsMap.containsKey(targetUserId)) {
        cvs.value = _userCVsMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await apiProvider.getUserCVs()
          : await apiProvider.getUserCVsById(targetUserId);

      if (response.success) {
        _userCVsMap[targetUserId] = response.cvs;
        cvs.value = response.cvs;
        _lastCVsUpdateMap[targetUserId] = DateTime.now();
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> uploadCV(
      {required String type, String? file, String? text}) async {
    try {
      final formData = dio.FormData.fromMap({
        'cv_type': type,
        if (type != "text")
          'cv_file':
              file == null ? null : await dio.MultipartFile.fromFile(file),
        if (type == "text") 'text': text,
      });

      await apiProvider.updateCv(formData);
      // await loadCVs(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  Future<void> updateCVSettings(
      String cvId, bool isPublic, bool isDefault) async {
    try {
      if (isDefault) {
        await authenticatedApiProvider.makeDefaultCV(cvId);
      }
      await authenticatedApiProvider
          .updateCVPrivacy(cvId, {'is_public': isPublic});
      await loadCVs(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  Future<void> deleteCV(String cvId) async {
    try {
      await authenticatedApiProvider.deleteCV(cvId);
      await loadCVs(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  Future<void> openCVInBrowser(CV cv) async {
    try {
      // Check if the URL is valid
      if (cv.fileUrl.isEmpty) {
        throw Exception('رابط الملف غير صالح');
      }

      // Show a loading snackbar
      Get.snackbar(
        'جاري الفتح',
        'يتم فتح الملف في المتصفح...',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

      // Use url_launcher to open the URL in browser
      final Uri url = Uri.parse(cv.fileUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);

        Get.snackbar(
          'تم الفتح',
          'تم فتح الملف في المتصفح',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } else {
        throw Exception('لا يمكن فتح الرابط: ${cv.fileUrl}');
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء فتح الملف: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      // Log the error
      Get.log('Error opening CV in browser: $e');
    }
  }

  // Privacy methods
  Future<void> updatePrivacySettings(Map<String, bool> settings) async {
    try {
      final response =
          await authenticatedApiProvider.updatePrivacySettings(settings);
      if (response.success) {
        // Refresh all data after privacy update
        await Future.wait([
          loadSkills(forceRefresh: true),
          loadExperiences(forceRefresh: true),
          loadAchievements(forceRefresh: true),
          loadCVs(forceRefresh: true),
        ]);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  // Get profile method (from original ProfileController)
  Future<dynamic> getProfile(String userId) async {
    try {
      final apiProvider = AppConfig.apiProvider;
      final response = await apiProvider.getProfile(userId);
      return response;
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // Image cropping and profile image update methods

  /// Updates profile image with cropping functionality
  /// This method handles the complete workflow: selection -> cropping -> upload
  Future<File?> updateProfileImageWithCropping(XFile imageFile) async {
    // 🧪 DEBUG CONSOLE TEST - Image cropping workflow
    print('📸 ProfileController: updateProfileImageWithCropping() started');
    debugPrint('📸 ProfileController: Image file path: ${imageFile.path}');
    developer.log('📸 Starting profile image cropping workflow',
        name: 'ProfileController', time: DateTime.now());
    DebugService.imageCropper('Starting profile image cropping workflow',
        data: {'imagePath': imageFile.path});

    try {
      print('📸 ProfileController: Showing cropping loader...');
      DebugService.imageCropper('Showing cropping loader');

      // Show loading dialog
      ImageCroppingService.showCroppingLoader();

      print('📸 ProfileController: Starting image cropping to square...');
      DebugService.imageCropper(
          'Starting image cropping to square aspect ratio');

      // Crop the image to square aspect ratio
      final croppedImage = await ImageCroppingService.cropImageToSquare(
        imageFile: imageFile,
        title: 'قص صورة الملف الشخصي',
      );

      print('📸 ProfileController: Hiding cropping loader...');
      DebugService.imageCropper('Hiding cropping loader');

      // Hide loading dialog
      ImageCroppingService.hideCroppingLoader();

      if (croppedImage == null) {
        print('📸 ProfileController: User cancelled cropping operation');
        DebugService.imageCropper('User cancelled cropping operation');
        // User cancelled cropping
        return null;
      }

      print('📸 ProfileController: Image cropping completed successfully');
      DebugService.imageCropper('Image cropping completed successfully',
          data: {'croppedImagePath': croppedImage.path});

      // Optimize the cropped image for upload
      final optimizedImage =
          await ImageCroppingService.optimizeImageForUpload(croppedImage);

      if (optimizedImage == null) {
        Get.snackbar(
          'خطأ',
          'فشل في تحسين الصورة للرفع',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return null;
      }

      // Upload the cropped and optimized image
      return optimizedImage;
    } catch (e) {
      // Hide loading dialog if still open
      ImageCroppingService.hideCroppingLoader();

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث صورة الملف الشخصي: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    }
  }

  /// Private method to handle the actual image upload
  Future<bool> _uploadProfileImage(File imageFile) async {
    try {
      // Use AuthService to update profile image (maintains existing workflow)
      await AuthService.instance.updateProfileImage(imageFile);
      return true;
    } catch (e) {
      CommonFunctions.handleError(e);
      return false;
    }
  }

  /// Updates profile cover image with cropping (optional square cropping)
  Future<bool> updateCoverImageWithCropping(XFile imageFile,
      {bool forceSquare = false}) async {
    try {
      File? finalImage;

      if (forceSquare) {
        // Show confirmation dialog for cropping
        final shouldCrop = await ImageCroppingService.showCroppingConfirmation(
          message: 'هل تريد قص صورة الغلاف لتصبح مربعة الشكل؟',
          title: 'قص صورة الغلاف',
        );

        if (!shouldCrop) {
          // Use original image without cropping
          finalImage = File(imageFile.path);
        } else {
          // Show loading dialog
          ImageCroppingService.showCroppingLoader();

          // Crop the image to square aspect ratio
          final croppedImage = await ImageCroppingService.cropImageToSquare(
            imageFile: imageFile,
            title: 'قص صورة الغلاف',
          );

          // Hide loading dialog
          ImageCroppingService.hideCroppingLoader();

          if (croppedImage == null) {
            // User cancelled cropping, use original
            finalImage = File(imageFile.path);
          } else {
            finalImage = croppedImage;
          }
        }
      } else {
        // Use original image without cropping
        finalImage = File(imageFile.path);
      }

      // Optimize the image for upload
      final optimizedImage =
          await ImageCroppingService.optimizeImageForUpload(finalImage);

      if (optimizedImage == null) {
        Get.snackbar(
          'خطأ',
          'فشل في تحسين الصورة للرفع',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }

      // Upload the image using AuthService
      await AuthService.instance.updateProfileCover(optimizedImage);

      Get.snackbar(
        'تم بنجاح',
        'تم تحديث صورة الغلاف بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      // Hide loading dialog if still open
      ImageCroppingService.hideCroppingLoader();

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث صورة الغلاف: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// 🧪 DEBUG CONSOLE TEST METHOD
  /// Call this method to test all debug output methods in ProfileController
  /// This method can be called from UI to verify debug console is working
  void testDebugConsoleOutput() {
    print('🧪 =================================');
    print('🧪 DEBUG CONSOLE TEST - ProfileController');
    print('🧪 =================================');

    // Test basic print statements
    print('🔍 TEST 1: Basic print() statement working');
    print('🔍 TEST 1: Current time: ${DateTime.now()}');

    // Test debugPrint
    debugPrint('🐛 TEST 2: debugPrint() statement working');
    debugPrint('🐛 TEST 2: ProfileController debug test');

    // Test developer.log
    developer.log('📝 TEST 3: developer.log() working',
        name: 'ProfileController');
    developer.log('📝 TEST 3: Structured logging test',
        name: 'ProfileController', time: DateTime.now());

    // Test DebugService
    DebugService.info('TEST 4: DebugService.info() working',
        category: DebugService.categoryProfile);
    DebugService.debug('TEST 4: DebugService.debug() working',
        category: DebugService.categoryProfile);
    DebugService.imageCropper('TEST 4: DebugService.imageCropper() working');
    DebugService.error(
        'TEST 4: DebugService.error() working (this is a test error)',
        category: DebugService.categoryProfile);

    // Test conditional debug output
    if (kDebugMode) {
      print('🔧 TEST 5: kDebugMode conditional working - app is in debug mode');
      DebugService.verbose('TEST 5: Verbose debug output in debug mode',
          category: DebugService.categoryProfile);
    }

    // Test error handling
    try {
      throw Exception('Test exception for debug console verification');
    } catch (error, stackTrace) {
      print('❌ TEST 6: Exception handling - $error');
      developer.log('TEST 6: Exception caught',
          name: 'ProfileController', error: error, stackTrace: stackTrace);
      DebugService.error('TEST 6: Exception handling test',
          category: DebugService.categoryProfile,
          error: error,
          stackTrace: stackTrace);
    }

    print('🧪 =================================');
    print('🧪 DEBUG CONSOLE TEST COMPLETED');
    print('🧪 Check VS Code Debug Console for all messages above');
    print('🧪 =================================');

    // Show snackbar to confirm test was run
    Get.snackbar(
      'Debug Test',
      'Debug console test completed. Check VS Code Debug Console for output.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }
}
